{"common": {"appName": "DeepWiki-Open", "tagline": "Documentación impulsada por IA", "generateWiki": "Generar Wiki", "processing": "Procesando...", "error": "Error", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "loading": "Cargando..."}, "loading": {"initializing": "Inicializando generación de wiki...", "fetchingStructure": "Obteniendo estructura del repositorio...", "determiningStructure": "Determinando estructura del wiki...", "clearingCache": "Limpiando caché del servidor...", "preparingDownload": "Por favor espere mientras preparamos su descarga..."}, "home": {"welcome": "Bienvenido a DeepWiki", "welcomeTagline": "Documentación impulsada por IA para repositorios de código", "description": "Genera documentación completa de repositorios GitHub, GitLab o Bitbucket con solo unos clics.", "quickStart": "<PERSON><PERSON><PERSON>", "enterRepoUrl": "Ingresa una URL de repositorio en uno de estos formatos:", "advancedVisualization": "Visualización Avanzada con Diagramas Mermaid", "diagramDescription": "DeepWiki genera automáticamente diagramas interactivos para ayudarte a entender la estructura y relaciones del código:", "flowDiagram": "Diagrama de Flujo", "sequenceDiagram": "Diagrama de Secuencia"}, "form": {"repository": "Repositorio", "configureWiki": "Configurar Wiki", "repoPlaceholder": "propietario/repositorio o URL de GitHub/GitLab/Bitbucket", "wikiLanguage": "Idioma del Wiki", "modelOptions": "Opciones de Modelo", "modelProvider": "<PERSON><PERSON><PERSON><PERSON>", "modelSelection": "Selección de Modelo", "wikiType": "Tipo de Wiki", "comprehensive": "Completo", "concise": "<PERSON><PERSON><PERSON>", "comprehensiveDescription": "Wiki detallado con secciones estructuradas y más páginas", "conciseDescription": "Wiki simplificado con menos páginas e información esencial", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (Local)", "localOllama": "Modelo <PERSON>ma Local", "experimental": "Experimental", "useOpenRouter": "Usar API de OpenRouter", "openRouterModel": "<PERSON><PERSON> OpenRouter", "useOpenai": "Usar API de Openai", "openaiModel": "<PERSON><PERSON>", "useCustomModel": "Usar modelo personalizado", "customModelPlaceholder": "Ingrese nombre de modelo personalizado", "addTokens": "+ Agregar tokens de acceso para repositorios privados", "hideTokens": "- Ocultar tokens de acceso", "accessToken": "Token de Acceso para Repositorios Privados", "selectPlatform": "Seleccionar <PERSON>", "personalAccessToken": "Token de Acceso Personal de {platform}", "tokenPlaceholder": "Ingresa tu token de {platform}", "tokenSecurityNote": "El token solo se almacena en memoria y nunca se persiste.", "defaultFiltersInfo": "Los filtros predeterminados incluyen directorios comunes como node_modules, .git y archivos de artefactos de construcción comunes.", "fileFilterTitle": "Configuración de Filtros de Archivos", "advancedOptions": "Opciones Avanzadas", "viewDefaults": "Ver Filtros Predeterminados", "showFilters": "<PERSON><PERSON>", "hideFilters": "O<PERSON>lta<PERSON>", "excludedDirs": "Directorios a Excluir", "excludedDirsHelp": "Una ruta de directorio por línea. Las rutas que comienzan con ./ son relativas a la raíz del repositorio.", "enterExcludedDirs": "Ingrese directorios a excluir, uno por línea...", "excludedFiles": "Archivos a Excluir", "excludedFilesHelp": "Un nombre de archivo por línea. Se admiten comodines (*).", "enterExcludedFiles": "Ingrese archivos a excluir, uno por línea...", "defaultFilters": "Archivos y Directorios Excluidos por Defecto", "directories": "Director<PERSON>", "files": "Archivos", "scrollToViewMore": "<PERSON>p<PERSON><PERSON> para ver más", "changeModel": "Cambiar <PERSON>o", "defaultNote": "Estos valores predeterminados ya están aplicados. Agregue sus exclusiones personalizadas arriba.", "hideDefault": "Ocultar Predeterminados", "viewDefault": "Ver Predeterminados", "authorizationCode": "Código de Autorización", "authorizationRequired": "Generar Wiki requiere código de autorización"}, "footer": {"copyright": "DeepWiki - Documentación impulsada por IA para repositorios de código"}, "ask": {"placeholder": "Haz una pregunta sobre este repositorio...", "askButton": "Preguntar", "deepResearch": "Investigación Profunda", "researchInProgress": "Investigación en progreso...", "continueResearch": "Continuar Investigación", "viewPlan": "Ver Plan", "viewUpdates": "Ver Actualizaciones", "viewConclusion": "Ver Conclusión"}, "repoPage": {"refreshWiki": "Actualizar Wiki", "confirmRefresh": "Confirmar Actualización", "cancel": "<PERSON><PERSON><PERSON>", "home": "<PERSON><PERSON>o", "errorTitle": "Error", "errorMessageDefault": "Por favor, compruebe que su repositorio existe y es público. Los formatos válidos son \"owner/repo\", \"https://github.com/owner/repo\", \"https://gitlab.com/owner/repo\", \"https://bitbucket.org/owner/repo\", o rutas de carpetas locales como \"C:\\\\path\\\\to\\\\folder\" o \"/path/to/folder\".", "backToHome": "Volver al Inicio", "exportWiki": "Exportar Wiki", "exportAsMarkdown": "Exportar como Markdown", "exportAsJson": "Exportar como JSON", "pages": "<PERSON><PERSON><PERSON><PERSON>", "relatedFiles": "Archivos Relacionados:", "relatedPages": "Páginas Relacionadas:", "selectPagePrompt": "Seleccione una página de la navegación para ver su contenido", "askAboutRepo": "Hacer preguntas sobre este repositorio"}, "nav": {"wikiProjects": "Lista de Proyectos"}, "projects": {"title": "Proyectos Wiki Procesados", "searchPlaceholder": "Buscar proyectos por nombre, propietario o repositorio...", "noProjects": "No se encontraron proyectos en la caché del servidor. La caché podría estar vacía o el servidor encontró un problema.", "noSearchResults": "Ningún proyecto coincide con sus criterios de búsqueda.", "processedOn": "Procesado el:", "loadingProjects": "Cargando proyectos...", "errorLoading": "Error al cargar proyectos:", "backToHome": "Volver al Inicio", "browseExisting": "Explorar Proyectos Existentes", "existingProjects": "Proyectos Existentes", "recentProjects": "Proyectos Recientes"}}