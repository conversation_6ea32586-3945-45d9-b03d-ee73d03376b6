{"common": {"appName": "DeepWiki-Open", "tagline": "Tài liệu hỗ trợ bởi AI", "generateWiki": "Tạo Wiki", "processing": "<PERSON><PERSON> xử lý...", "error": "Lỗi", "submit": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "loading": "<PERSON><PERSON> tả<PERSON>..."}, "loading": {"initializing": "Đang khởi tạo wiki...", "fetchingStructure": "<PERSON><PERSON> l<PERSON>y cấu trúc repository...", "determiningStructure": "<PERSON><PERSON> x<PERSON>c đ<PERSON>nh cấu trúc wiki...", "clearingCache": "<PERSON><PERSON> x<PERSON>a bộ nhớ đệm máy chủ...", "preparingDownload": "<PERSON><PERSON> tải! Vui lòng chờ..."}, "home": {"welcome": "<PERSON><PERSON><PERSON> mừng đến với DeepWiki-Open", "welcomeTagline": "Tài liệu hỗ trợ bởi AI cho các repository của bạn", "description": "Tạo tài liệu từ các repository GitHub, GitLab, hoặc Bitbucket chỉ với vài cú nhấp chuột.", "quickStart": "<PERSON><PERSON><PERSON> đ<PERSON> n<PERSON>h", "enterRepoUrl": "Nhập URL repository", "advancedVisualization": "<PERSON><PERSON><PERSON> chỉnh sơ đồ trực quan với Mermaid", "diagramDescription": "DeepWiki tự động tạo các sơ đồ tương tác giúp bạn hiểu cấu trúc source codes và mối quan hệ giữa chúng:", "flowDiagram": "S<PERSON> đồ luồng", "sequenceDiagram": "<PERSON><PERSON> đồ tuần tự"}, "form": {"repository": "Repository", "configureWiki": "<PERSON><PERSON><PERSON> h<PERSON> W<PERSON>", "repoPlaceholder": "owner/repo hoặc URL GitHub/GitLab/Bitbucket", "wikiLanguage": "<PERSON><PERSON><PERSON>", "modelOptions": "<PERSON><PERSON><PERSON> chọn mô hình", "modelProvider": "<PERSON><PERSON><PERSON> cung cấp mô hình", "modelSelection": "<PERSON><PERSON><PERSON> chọn mô hình", "wikiType": "Loại Wiki", "comprehensive": "<PERSON><PERSON><PERSON>", "concise": "<PERSON><PERSON><PERSON> t<PERSON>ch", "comprehensiveDescription": "Wiki chi tiết với các phần có cấu trúc và nhiều trang hơn", "conciseDescription": "Wiki đơn giản hóa với ít trang hơn và thông tin thiết yếu", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (Cục bộ)", "localOllama": "<PERSON><PERSON> <PERSON> c<PERSON> bộ", "experimental": "<PERSON><PERSON><PERSON>", "useOpenRouter": "Sử dụng API OpenRouter", "openRouterModel": "<PERSON><PERSON>Router", "useOpenai": "Sử dụng API Openai", "openaiModel": "<PERSON><PERSON>", "useCustomModel": "Sử dụng mô hình tùy chỉnh", "customModelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên mô hình tùy chỉnh", "addTokens": "+ Thêm token truy cập cho private repositories", "hideTokens": "- Ẩn token truy cập", "accessToken": "Token truy cập cho private repositories", "selectPlatform": "<PERSON><PERSON><PERSON> n<PERSON>n tảng", "personalAccessToken": "Token truy cập cá nhân {platform}", "tokenPlaceholder": "Nhập token {platform} c<PERSON><PERSON> bạn", "tokenSecurityNote": "Token chỉ được lưu trong bộ nhớ và không bao giờ được lưu trữ vĩnh viễn.", "defaultFiltersInfo": "Lọ<PERSON> mặc định bao gồm các thư mục thông thường như node_modules, .git và các tệp tài liệu xây dựng thông thường.", "fileFilterTitle": "<PERSON><PERSON><PERSON> <PERSON>", "advancedOptions": "<PERSON><PERSON><PERSON> ch<PERSON>n nâng cao", "viewDefaults": "<PERSON><PERSON> Mặc định", "showFilters": "<PERSON><PERSON><PERSON> <PERSON>", "hideFilters": "Ẩn <PERSON><PERSON><PERSON>", "excludedDirs": "<PERSON><PERSON><PERSON> mục đ<PERSON> trừ", "excludedDirsHelp": "Một đường dẫn thư mục trên một dòng. Đường dẫn bắt đầu bằng ./ là tương đối so với gốc kho lưu trữ.", "enterExcludedDirs": "<PERSON><PERSON><PERSON><PERSON> thư mục cần lo<PERSON>i trừ, mỗi dòng một thư mục...", "excludedFiles": "<PERSON><PERSON><PERSON> <PERSON> trừ", "excludedFilesHelp": "<PERSON><PERSON>t tên tệp trên một dòng. Hỗ trợ ký tự đại diện (*).", "enterExcludedFiles": "<PERSON><PERSON><PERSON><PERSON> tệp cần lo<PERSON>i trừ, mỗi dòng một tệp...", "defaultFilters": "<PERSON>ệ<PERSON> và <PERSON><PERSON><PERSON> mụ<PERSON> trừ Mặc định", "directories": "<PERSON><PERSON><PERSON>", "files": "<PERSON><PERSON><PERSON>", "scrollToViewMore": "<PERSON><PERSON><PERSON> chuyển để xem thêm", "changeModel": "<PERSON>hay đổi mô hình", "defaultNote": "<PERSON><PERSON><PERSON> giá trị mặc định này đã được áp dụng. Thêm các loại trừ tùy chỉnh của bạn ở trên.", "hideDefault": "Ẩn mặc định", "viewDefault": "<PERSON><PERSON> mặc định", "authorizationCode": "<PERSON><PERSON> xác thực", "authorizationRequired": "<PERSON><PERSON> xác thực cần thiết để tạo Wiki"}, "footer": {"copyright": "DeepWiki - <PERSON><PERSON><PERSON> li<PERSON>u hỗ trợ bởi AI cho repository"}, "ask": {"placeholder": "Đặt một câu hỏi về repository này...", "askButton": "Hỏi", "deepResearch": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u sâu", "researchInProgress": "<PERSON><PERSON> tiến hành nghiên cứu...", "continueResearch": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> ng<PERSON>", "viewPlan": "<PERSON><PERSON> ho<PERSON>", "viewUpdates": "<PERSON><PERSON> c<PERSON> nh<PERSON>t", "viewConclusion": "<PERSON><PERSON> k<PERSON> lu<PERSON>n"}, "repoPage": {"refreshWiki": "<PERSON>àm mới Wiki", "confirmRefresh": "<PERSON><PERSON><PERSON> n<PERSON>n làm mới", "cancel": "Hủy bỏ", "home": "Trang chủ", "errorTitle": "Lỗi", "errorMessageDefault": "<PERSON>ui lòng kiểm tra xem repository có tồn tại và công khai hay không. <PERSON><PERSON><PERSON> định dạng hợp lệ là \"owner/repo\", \"https://github.com/owner/repo\", \"https://gitlab.com/owner/repo\", \"https://bitbucket.org/owner/repo\", hoặc các đường dẫn thư mục cục bộ như \"C:\\\\path\\\\to\\\\folder\" hoặc \"/path/to/folder\".", "backToHome": "Quay lại trang chủ", "exportWiki": "Xuất Wiki", "exportAsMarkdown": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON> dạng <PERSON>down", "exportAsJson": "<PERSON><PERSON>t dư<PERSON>i dạng JSON", "pages": "<PERSON><PERSON>", "relatedFiles": "<PERSON><PERSON><PERSON> liên quan:", "relatedPages": "<PERSON><PERSON> liên quan:", "selectPagePrompt": "<PERSON><PERSON><PERSON> một trang từ thanh điều hướng để xem nội dung của nó", "askAboutRepo": "Hỏi về repository này"}, "nav": {"wikiProjects": "<PERSON><PERSON> s<PERSON>ch d<PERSON>n"}, "projects": {"title": "Dự án Wiki đã xử lý", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm dự án theo tên, chủ sở hữu hoặc repository...", "noProjects": "<PERSON>h<PERSON>ng tìm thấy dự án nào trong bộ nhớ đệm máy chủ. Bộ nhớ đệm có thể trống hoặc máy chủ gặp sự cố.", "noSearchResults": "<PERSON>h<PERSON>ng có dự án nào phù hợp với tiêu chí tìm kiếm của bạn.", "processedOn": "Xử lý vào:", "loadingProjects": "<PERSON><PERSON> tải dự án...", "errorLoading": "Lỗi khi tải dự án:", "backToHome": "<PERSON><PERSON> trang chủ", "browseExisting": "Duyệt dự án hiện có", "existingProjects": "Dự án hiện có", "recentProjects": "<PERSON><PERSON> án gần đây"}}