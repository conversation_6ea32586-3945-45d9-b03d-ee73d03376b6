{"common": {"appName": "DeepWiki-Open", "tagline": "AI-powered documentation", "generateWiki": "Generate Wiki", "processing": "Processing...", "error": "Error", "submit": "Submit", "cancel": "Cancel", "close": "Close", "loading": "Loading..."}, "loading": {"initializing": "Initializing wiki generation...", "fetchingStructure": "Fetching repository structure...", "determiningStructure": "Determining wiki structure...", "clearingCache": "Clearing server cache...", "preparingDownload": "Please wait while we prepare your download..."}, "home": {"welcome": "Welcome to DeepWiki-Open", "welcomeTagline": "AI-powered documentation for your code repositories", "description": "Generate comprehensive documentation from GitHub, GitLab, or Bitbucket repositories with just a few clicks.", "quickStart": "Quick Start", "enterRepoUrl": "Enter a repository URL in one of these formats:", "advancedVisualization": "Advanced Visualization with Mermaid Diagrams", "diagramDescription": "DeepWiki automatically generates interactive diagrams to help you understand code structure and relationships:", "flowDiagram": "Flow Diagram", "sequenceDiagram": "Sequence Diagram"}, "form": {"repository": "Repository", "configureWiki": "Configure Wiki", "repoPlaceholder": "owner/repo or GitHub/GitLab/Bitbucket URL", "wikiLanguage": "Wiki Language", "modelOptions": "Model Options", "modelProvider": "Model Provider", "modelSelection": "Model Selection", "wikiType": "Wiki Type", "comprehensive": "Comprehensive", "concise": "Concise", "comprehensiveDescription": "Detailed wiki with structured sections and more pages", "conciseDescription": "Simplified wiki with fewer pages and essential information", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (Local)", "localOllama": "Local Ollama Model", "experimental": "Experimental", "useOpenRouter": "Use OpenRouter API", "openRouterModel": "OpenRouter Model", "useOpenai": "Use Openai API", "openaiModel": "Openai Model", "useCustomModel": "Use custom model", "customModelPlaceholder": "Enter custom model name", "addTokens": "+ Add access tokens for private repositories", "hideTokens": "- Hide access tokens", "accessToken": "Access Token for Private Repositories", "selectPlatform": "Select Platform", "personalAccessToken": "{platform} Personal Access Token", "tokenPlaceholder": "Enter your {platform} token", "tokenSecurityNote": "Token is stored in memory only and never persisted.", "defaultFiltersInfo": "Default filters include common directories like node_modules, .git, and common build artifact files.", "fileFilterTitle": "File Filter Configuration", "advancedOptions": "Advanced Options", "viewDefaults": "View Default Filters", "showFilters": "Show Filters", "hideFilters": "Hide Filters", "excludedDirs": "Directories to Exclude", "excludedDirsHelp": "One directory path per line. Paths starting with ./ are relative to repository root.", "enterExcludedDirs": "Enter excluded directories, one per line...", "excludedFiles": "Files to Exclude", "excludedFilesHelp": "One filename per line. Wildcards (*) are supported.", "enterExcludedFiles": "Enter excluded files, one per line...", "defaultFilters": "Default Excluded Files & Directories", "directories": "Directories", "files": "Files", "scrollToViewMore": "Scroll to view more", "changeModel": "Change Model", "defaultNote": "These defaults are already applied. Add your custom exclusions above.", "hideDefault": "<PERSON><PERSON>", "viewDefault": "View Default", "includedDirs": "Included Directories", "includedFiles": "Included Files", "enterIncludedDirs": "Enter included directories, one per line...", "enterIncludedFiles": "Enter included files, one per line...", "filterMode": "Filter Mode", "excludeMode": "Exclude Paths", "includeMode": "Include Only Paths", "excludeModeDescription": "Specify paths to exclude from processing (default behavior)", "includeModeDescription": "Specify only the paths to include, ignoring all others", "authorizationCode": "Authorization Code", "authorizationRequired": "Authentication is required to generate the wiki."}, "footer": {"copyright": "DeepWiki - AI-powered documentation for code repositories"}, "ask": {"placeholder": "Ask a question about this repository...", "askButton": "Ask", "deepResearch": "Deep Research", "researchInProgress": "Research in progress...", "continueResearch": "Continue Research", "viewPlan": "View Plan", "viewUpdates": "View Updates", "viewConclusion": "View Conclusion"}, "repoPage": {"refreshWiki": "Refresh Wiki", "confirmRefresh": "Confirm Refresh", "cancel": "Cancel", "home": "Home", "errorTitle": "Error", "errorMessageDefault": "Please check that your repository exists and is public. Valid formats are \"owner/repo\", \"https://github.com/owner/repo\", \"https://gitlab.com/owner/repo\", \"https://bitbucket.org/owner/repo\", or local folder paths like \"C:\\\\path\\\\to\\\\folder\" or \"/path/to/folder\".", "backToHome": "Back to Home", "exportWiki": "Export Wiki", "exportAsMarkdown": "Export as <PERSON><PERSON>", "exportAsJson": "Export as JSON", "pages": "Pages", "relatedFiles": "Related Files:", "relatedPages": "Related Pages:", "selectPagePrompt": "Select a page from the navigation to view its content", "askAboutRepo": "Ask questions about this repository"}, "nav": {"wikiProjects": "Wiki Projects"}, "projects": {"title": "Processed Wiki Projects", "searchPlaceholder": "Search projects by name, owner, or repository...", "noProjects": "No projects found in the server cache. The cache might be empty or the server encountered an issue.", "noSearchResults": "No projects match your search criteria.", "processedOn": "Processed on:", "loadingProjects": "Loading projects...", "errorLoading": "Error loading projects:", "backToHome": "Back to Home", "browseExisting": "Browse Existing Projects", "existingProjects": "Existing Projects", "recentProjects": "Recent Projects"}}